<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title><PERSON><PERSON> – Multi-Purpose Discord <PERSON></title>
  <style>
    :root {
      --bg: #0d1117;
      --card: #161b22;
      --accent: #238636;
      --accent-hover: #2ea043;
      --border: #30363d;
      --text: #e6edf3;
      --subtext: #8b949e;
      --link: #58a6ff;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    }

    body {
      background: var(--bg);
      color: var(--text);
      line-height: 1.6;
      min-height: 100vh;
      display: flex;
      justify-content: center;
      align-items: flex-start;
      padding: 40px 20px;
    }

    .container {
      width: 100%;
      max-width: 900px;
    }

    .header {
      text-align: center;
      margin-bottom: 50px;
    }

    .header h1 {
      font-size: 3em;
      font-weight: 600;
      margin-bottom: 10px;
      color: var(--text);
    }

    .header p {
      font-size: 1.1em;
      color: var(--subtext);
    }

    .section {
      background: var(--card);
      border: 1px solid var(--border);
      border-radius: 8px;
      padding: 30px;
      margin-bottom: 40px;
    }

    .section h2 {
      font-size: 1.8em;
      font-weight: 600;
      margin-bottom: 20px;
      color: var(--text);
      border-bottom: 1px solid var(--border);
      padding-bottom: 10px;
    }

    .section p,
    .section li {
      color: #c9d1d9;
    }

    .btn {
      display: inline-block;
      padding: 12px 24px;
      border-radius: 6px;
      font-weight: 500;
      text-decoration: none;
      transition: background 0.2s ease, transform 0.2s ease;
      margin: 10px 8px 0 0;
    }

    .btn-primary {
      background: var(--accent);
      color: white;
    }

    .btn-primary:hover {
      background: var(--accent-hover);
      transform: translateY(-1px);
    }

    .btn-secondary {
      background: transparent;
      color: var(--link);
      border: 1px solid var(--border);
    }

    .btn-secondary:hover {
      background: #21262d;
      border-color: var(--link);
    }

    .feature-list ul {
      list-style: none;
      padding-left: 0;
    }

    .feature-list li::before {
      content: "•";
      color: var(--link);
      margin-right: 8px;
    }

    .command-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
    }

    .command-card {
      background: #21262d;
      border: 1px solid var(--border);
      border-radius: 6px;
      padding: 20px;
    }

    .command-card .command-name {
      font-weight: bold;
      color: var(--text);
      font-size: 1.1em;
      margin-bottom: 8px;
    }

    code {
      background: #30363d;
      padding: 3px 6px;
      border-radius: 4px;
      font-family: Consolas, monospace;
      font-size: 0.95em;
    }

    .footer {
      text-align: center;
      color: var(--subtext);
      margin-top: 50px;
      font-size: 0.95em;
    }

    .footer a {
      color: var(--link);
    }

    @media (max-width: 600px) {
      .header h1 {
        font-size: 2em;
      }

      .btn {
        display: block;
        width: 100%;
        margin: 10px 0;
        text-align: center;
      }
    }
  </style>
  <script
    src="https://cdn.sell.app/embed/script.js"
    type="module"
  >
  </script>
  <link
    href="https://cdn.sell.app/embed/style.css"
    rel="stylesheet"
  />
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Leakin Bot</h1>
      <p>Multi-purpose Discord bot with licensing system</p>
    </div>

    <div class="section">
      <h2>Get Started</h2>
      <p>Purchase a license and add Leakin Bot to your Discord server.</p>
        <!-- Sell.app purchase button -->
        <button
          data-sell-store="64254"
          data-sell-product="311459"
          data-sell-theme=""
          data-sell-darkmode="true"
          class="btn btn-secondary"
        >
          Purchase License
        </button>
      <a href="https://discord.com/oauth2/authorize?client_id=1389466386396483714" target="_blank" class="btn btn-primary">Add to Server</a>
    </div>

    <div class="section">
      <h2>Features</h2>
      <div class="feature-list">
        <ul>
          <li>Status-based auto role assignment (repping system)</li>
          <li>Temporary voice channels with full management</li>
          <li>Gender verification system with ticket support</li>
          <li>Anonymous venting system with moderation logging</li>
          <li>Multi-server configuration with individual licensing</li>
          <li>Comprehensive logging and monitoring</li>
          <li>Rate limit safe with fast updates</li>
        </ul>
      </div>
    </div>

    <!-- License Management Section -->
    <div class="section">
      <h2>🔑 License Management</h2>
      <p><strong>Setup:</strong> Purchase a license at <a href="https://leakin.cc" target="_blank">leakin.cc</a>, then redeem it with <code>/redeem-key</code>. Each server requires its own license.</p>
      <div class="command-grid">
        <div class="command-card">
          <div class="command-name">/redeem-key</div>
          <p>Redeem license for your server (Server Owner only)</p>
          <code>/redeem-key ABC123XYZ</code>
        </div>
        <div class="command-card">
          <div class="command-name">/my-keys</div>
          <p>View all your license keys</p>
          <code>/my-keys</code>
        </div>
        <div class="command-card">
          <div class="command-name">/transfer-key</div>
          <p>Transfer your license key to another server</p>
          <code>/transfer-key</code>
        </div>
        <div class="command-card">
          <div class="command-name">/transfer-key-user</div>
          <p>Transfer your license key to another user</p>
          <code>/transfer-key-user</code>
        </div>
        <div class="command-card">
          <div class="command-name">/server-status</div>
          <p>View server configuration and license status</p>
          <code>/server-status</code>
        </div>
      </div>
    </div>

    <!-- Repping System Section -->
    <div class="section">
      <h2>⭐ Repping System (Status-Based Roles)</h2>
      <p><strong>Setup:</strong> Use <code>/rep</code> to configure everything at once, then optionally add ignored users and set up logging.</p>
      <div class="command-grid">
        <div class="command-card">
          <div class="command-name">/rep</div>
          <p>Set up complete repping system (trigger word, role, and notification channel)</p>
          <code>/rep /leakin @VIPRole #notifications</code>
        </div>
        <div class="command-card">
          <div class="command-name">/add-ignored-user</div>
          <p>Add a user to the ignored list (e.g., bots)</p>
          <code>/add-ignored-user @SomeBot</code>
        </div>
        <div class="command-card">
          <div class="command-name">/set-log-id</div>
          <p>Set channel for detailed role change logs</p>
          <code>/set-log-id #bot-logs</code>
        </div>
      </div>
    </div>

    <!-- TempVoice System Section -->
    <div class="section">
      <h2>🎤 Temporary Voice Channels</h2>
      <p><strong>Setup:</strong> Use <code>/temp-voice</code> to set up the system. Users join the creator channel to automatically get their own temporary voice channel with full management controls.</p>
      <div class="command-grid">
        <div class="command-card">
          <div class="command-name">/temp-voice</div>
          <p>Set up TempVoice system with interface and creator channel</p>
          <code>/temp-voice #interface #creator-vc 10</code>
        </div>
      </div>
      <p><strong>User Features:</strong> Once set up, users get an interface with buttons to set user limits, kick/block users, lock/unlock channels, and transfer ownership.</p>
    </div>

    <!-- Gender Verification Section -->
    <div class="section">
      <h2>✅ Gender Verification System</h2>
      <p><strong>Setup:</strong> Use <code>/gender-verification</code> to create a verification system with tickets. Users submit photos with handwritten text for manual verification.</p>
      <div class="command-grid">
        <div class="command-card">
          <div class="command-name">/gender-verification</div>
          <p>Set up gender verification with tickets and support role</p>
          <code>/gender-verification #verify-channel #tickets-category @SupportRole "Write your username"</code>
        </div>
      </div>
      <p><strong>How it works:</strong> Users click a button to start verification, submit a photo with required text, and support staff review in private tickets.</p>
    </div>

    <!-- Anonymous Venting Section -->
    <div class="section">
      <h2>💭 Anonymous Venting System</h2>
      <p><strong>Setup:</strong> Requires a log channel to be set first with <code>/set-log-id</code>, then use <code>/vent-setup</code> to configure the vent channel. All messages are logged for moderation.</p>
      <div class="command-grid">
        <div class="command-card">
          <div class="command-name">/vent-setup</div>
          <p>Set up anonymous venting channel (requires log channel first)</p>
          <code>/vent-setup #vent-channel</code>
        </div>
        <div class="command-card">
          <div class="command-name">/vent</div>
          <p>Send an anonymous message to the vent channel</p>
          <code>/vent Your anonymous message here</code>
        </div>
      </div>
      <p><strong>Privacy Note:</strong> While messages appear anonymous to users, all vent messages are logged with user information for moderation purposes.</p>
    </div>

    <!-- Sticky Messages Section -->
    <div class="section">
      <h2>📌 Sticky Messages System</h2>
      <p><strong>Setup:</strong> Requires a log channel to be set first with <code>/set-log-id</code>. Create persistent sticky messages that automatically repost when new messages are sent in the channel.</p>
      <div class="command-grid">
        <div class="command-card">
          <div class="command-name">/stick</div>
          <p>Create a persistent sticky message in a channel (Admin only)</p>
          <code>/stick #channel</code>
        </div>
        <div class="command-card">
          <div class="command-name">/unstick</div>
          <p>Remove the sticky message from a channel (Admin only)</p>
          <code>/unstick #channel</code>
        </div>
      </div>
      <p><strong>How it works:</strong> After running <code>/stick</code>, a modal form opens for you to enter your sticky message content (up to 4000 characters). The message will automatically repost whenever someone sends a new message in that channel, with 5-second rate limiting to prevent spam. Only one sticky message per channel is allowed.</p>
      <p><strong>Features:</strong> Rate limiting prevents spam, comprehensive logging to your log channel, persistence through bot restarts, and automatic cleanup when channels are deleted.</p>
    </div>

    <div class="footer">
      &copy; 2025 Leakin Bot – <a href="https://leakin.cc" target="_blank">leakin.cc</a>
    </div>
  </div>
</body>
</html>
